{"name": "nextlaunchpad", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint", "db:push": "npx dotenv-cli -e .env.local -- npx prisma db push", "db:pull": "npx dotenv-cli -e .env.local -- npx prisma db pull", "db:generate": "npx prisma generate", "db:studio": "npx dotenv-cli -e .env.local -- npx prisma studio", "db:sync": "npm run db:pull && npm run db:push && npm run db:generate", "postinstall": "prisma generate"}, "dependencies": {"@auth/core": "^0.34.2", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-slot": "^1.1.0", "@stripe/stripe-js": "^5.4.0", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.5.0", "lucide-react": "^0.468.0", "next": "15.0.3", "next-auth": "^4.24.11", "next-intl": "^3.26.3", "next-themes": "^0.4.6", "react": "^18.2.0", "react-dom": "^18.2.0", "sonner": "^1.7.1", "stripe": "^17.5.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.4"}, "devDependencies": {"@prisma/client": "^6.1.0", "@types/node": "^20.17.10", "@types/react": "^18", "@types/react-dom": "^18", "dotenv-cli": "^8.0.0", "eslint": "^8", "eslint-config-next": "15.0.3", "next-sitemap": "^4.2.3", "postcss": "^8", "prisma": "^6.1.0", "tailwindcss": "^3.4.1", "typescript": "^5"}, "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac"}