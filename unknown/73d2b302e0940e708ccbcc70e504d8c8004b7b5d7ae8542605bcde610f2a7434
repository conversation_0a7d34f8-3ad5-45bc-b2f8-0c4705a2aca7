import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import type { GameCardProps } from '@/data/games';

interface Props {
  game: GameCardProps;
  locale: string;
  priority?: boolean;
}

export default function GameCard({ game, locale, priority = false }: Props) {
  const t = useTranslations('Navigation.categoryList');

  return (
    <Link
      href={`/${locale}/game/${game.id}`}
      className="block group"
      aria-label={`${t('play')} ${game.title[locale as keyof typeof game.title] || game.title['en']}`}
      target="_blank"
      rel="noopener noreferrer"
    >
      <div className="relative aspect-video rounded-lg overflow-hidden bg-secondary">
        <Image
          src={game.image}
          alt={game.title[locale as keyof typeof game.title] || game.title['en']}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          width={320}
          height={180}
          priority={priority}
        />
      </div>
      <div className="mt-2 space-y-1">
        <h3 className="font-medium text-foreground line-clamp-1">
          {game.title[locale as keyof typeof game.title] || game.title['en']}
        </h3>
        <div className="flex items-center text-sm text-muted-foreground">
          <span className="line-clamp-1">
            {game.category[locale as keyof typeof game.category] || game.category['en']}
          </span>
          <span className="mx-2">•</span>
          <span className="flex items-center">
            ⭐ {game.rating}
          </span>
        </div>
      </div>
    </Link>
  );
} 