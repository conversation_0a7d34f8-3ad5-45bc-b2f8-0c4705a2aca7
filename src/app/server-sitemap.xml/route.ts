import { getServerSideSitemap } from 'next-sitemap';
import { routing } from '@/i18n/routing';
import type { ISitemapField } from 'next-sitemap';

export async function GET() {
  // 获取所有支持的语言
  const locales = routing.locales;
  
  // 获取当前日期时间戳
  const currentDate = new Date().toISOString();
  
  // 创建基本URL
  const baseUrl = process.env.SITE_URL || 'https://freehubgames.com';
  
  // 创建站点地图项
  const fields: ISitemapField[] = [];

  // 常见游戏类别列表
  const gameCategories = [
    'action', 'adventure', 'arcade', 'board', 'card', 
    'casino', 'casual', 'educational', 'family', 'fighting',
    'music', 'puzzle', 'racing', 'role-playing', 'shooter',
    'simulation', 'sports', 'strategy', 'trivia', 'word'
  ];
  
  // 为每种语言创建主页和分类页面
  for (const locale of locales) {
    // 主页
    fields.push({
      loc: `${baseUrl}/${locale}`,
      lastmod: currentDate,
      changefreq: 'daily',
      priority: 1.0
    });
    
    // 游戏分类页面
    fields.push({
      loc: `${baseUrl}/${locale}/categories`,
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: 0.8
    });
    
    // 游戏搜索页面
    fields.push({
      loc: `${baseUrl}/${locale}/search`,
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: 0.7
    });
    
    // 添加每个游戏类别页面
    for (const category of gameCategories) {
      fields.push({
        loc: `${baseUrl}/${locale}/games/${category}`,
        lastmod: currentDate,
        changefreq: 'weekly',
        priority: 0.6
      });
    }
    
    // 关于我们页面
    fields.push({
      loc: `${baseUrl}/${locale}/about`,
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: 0.5
    });
    
    // 联系我们页面
    fields.push({
      loc: `${baseUrl}/${locale}/contact`,
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: 0.5
    });
    
    // 站点地图页面
    fields.push({
      loc: `${baseUrl}/${locale}/sitemap`,
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: 0.3
    });
  }
  
  // TODO: 添加动态数据（例如从数据库获取的游戏列表、类别等）
  // 这部分代码可以从数据库或API获取真实数据
  // 例如: const games = await fetchGames();
  
  return getServerSideSitemap(fields);
} 