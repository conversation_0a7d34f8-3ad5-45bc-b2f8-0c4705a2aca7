'use client';

import React from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';

// 定义站点地图项的类型
interface SitemapItem {
  title: string;
  url: string;
  children?: SitemapItem[];
}

export default function SitemapPage() {
  const t = useTranslations('common');
  const { locale } = useParams();
  
  // 游戏类别列表
  const gameCategories = [
    { id: 'action', name: t('actionGames') },
    { id: 'adventure', name: t('adventureGames') },
    { id: 'arcade', name: t('arcadeGames') },
    { id: 'board', name: t('boardGames') },
    { id: 'card', name: t('cardGames') },
    { id: 'puzzle', name: t('puzzleGames') },
    { id: 'racing', name: t('racingGames') },
    { id: 'shooter', name: t('shooterGames') },
    { id: 'sports', name: t('sportsGames') },
    { id: 'strategy', name: t('strategyGames') }
  ];
  
  // 站点结构定义
  const siteStructure: SitemapItem[] = [
    {
      title: t('home'),
      url: `/${locale}`,
    },
    {
      title: t('games'),
      url: `/${locale}/games`,
      children: [
        {
          title: t('popularGames'),
          url: `/${locale}/games/popular`,
        },
        {
          title: t('newGames'),
          url: `/${locale}/games/new`,
        },
      ],
    },
    {
      title: t('categories'),
      url: `/${locale}/categories`,
      children: gameCategories.map(category => ({
        title: category.name,
        url: `/${locale}/games/${category.id}`,
      })),
    },
    {
      title: t('search'),
      url: `/${locale}/search`,
    },
    {
      title: t('about'),
      url: `/${locale}/about`,
    },
    {
      title: t('contact'),
      url: `/${locale}/contact`,
    },
  ];

  // 渲染站点地图项
  const renderSitemapItem = (item: SitemapItem, index: string | number, level: number = 0) => {
    return (
      <div key={index} style={{ marginLeft: `${level * 20}px` }} className="py-2">
        <Link 
          href={item.url}
          className="text-primary hover:underline font-medium"
        >
          {item.title}
        </Link>
        
        {item.children && (
          <div className="mt-2">
            {item.children.map((child: SitemapItem, childIndex: number) => 
              renderSitemapItem(child, `${index}-${childIndex}`, level + 1)
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{t('sitemap')}</h1>
        <p className="text-muted-foreground">
          {t('sitemapDescription')}
        </p>
      </div>
      
      <div className="bg-card rounded-lg border p-6">
        {siteStructure.map((item, index) => renderSitemapItem(item, index))}
      </div>
      
      <div className="mt-8 text-sm text-muted-foreground">
        <p>{t('lastUpdated')}: {new Date().toLocaleDateString()}</p>
        <p>
          <a href="/sitemap.xml" target="_blank" className="text-primary hover:underline">
            {t('xmlSitemap')}
          </a>
        </p>
      </div>
    </div>
  );
} 