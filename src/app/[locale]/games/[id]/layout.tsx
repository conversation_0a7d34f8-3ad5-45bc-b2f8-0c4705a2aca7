import { getTranslations } from 'next-intl/server';
import type { Metadata } from 'next';
import { use } from 'react';

interface CategoryLayoutProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
  children: React.ReactNode;
}

export async function generateMetadata({ params }: CategoryLayoutProps): Promise<Metadata> {
  const resolvedParams = await params;
  const { id, locale } = resolvedParams;
  const t = await getTranslations({ locale, namespace: 'Navigation.categoryList' });
  const categoryName = t(id as any);

  const title = `${categoryName} - FreeHubGames`;
  const description = locale === 'zh' 
    ? `在FreeHubGames上免费玩${categoryName}游戏。找到最好玩的${categoryName}游戏来娱乐。`
    : `Play free ${categoryName} games online at FreeHubGames. Find the best ${categoryName} games for your entertainment.`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
      locale: locale,
      alternateLocale: ['en', 'zh'].filter(l => l !== locale),
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    }
  };
}

export default function CategoryLayout({ children, params }: CategoryLayoutProps) {
  const resolvedParams = use(params);
  return children;
} 